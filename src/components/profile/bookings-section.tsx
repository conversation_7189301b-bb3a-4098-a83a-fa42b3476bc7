"use client";

import React, { useState } from "react";
import CourtBookingSection from "./bookings/court-booking-section";

interface BookingsSectionProps {
  activeSection: string;
}

const BookingsSection: React.FC<BookingsSectionProps> = ({ activeSection }) => {
  const [activeTab, setActiveTab] = useState("court");

  return (
    <div
      className={`flex-1 rounded-lg bg-white p-6 ${
        activeSection === "bookings-section" ? "" : "hidden"
      }`}
      id="bookings-section"
    >
      <h2 className="mb-6 text-2xl font-bold text-gray-900">My Bookings</h2>
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            className={`${activeTab === "court" ? "border-primary text-primary" : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"} border-b-2 px-1 py-4 text-sm font-medium whitespace-nowrap`}
            onClick={() => setActiveTab("court")}
          >
            Court
          </button>
          <button
            className={`${activeTab === "lesson" ? "border-primary text-primary" : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"} border-b-2 px-1 py-4 text-sm font-medium whitespace-nowrap`}
            onClick={() => setActiveTab("lesson")}
          >
            Lesson
          </button>
          <button
            className={`${activeTab === "program" ? "border-primary text-primary" : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"} border-b-2 px-1 py-4 text-sm font-medium whitespace-nowrap`}
            onClick={() => setActiveTab("program")}
          >
            Program
          </button>
        </nav>
      </div>
      <div className="mt-6">
        {activeTab === "court" && <CourtBookingSection />}
        {activeTab === "lesson" && <div>Lesson bookings content</div>}
        {activeTab === "program" && <div>Program bookings content</div>}
      </div>
    </div>
  );
};

export default BookingsSection;
